[gd_scene load_steps=5 format=3 uid="uid://bx8vn7qkqxqxq"]

[ext_resource type="Script" uid="uid://dcflxi1ai8tpb" path="res://scripts/MainMenu.gd" id="1_main"]
[ext_resource type="FontFile" uid="uid://md6m40unc1ik" path="res://assets/UI MMORPG Dark Templar Wenrexa/Font/Berry Rotunda.ttf" id="2_font"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2"]
bg_color = Color(0.2, 0.15, 0.1, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 0.9, 0.6, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.1, 0.1, 0.1, 0.8)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.8, 0.7, 0.4, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.05, 0.05, 1)

[node name="BackgroundTexture" type="TextureRect" parent="Background"]
modulate = Color(1, 1, 1, 0.3)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
stretch_mode = 6

[node name="TitleLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -400.0
offset_right = 300.0
offset_bottom = -300.0
grow_horizontal = 2
grow_vertical = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.8)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 3
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 48
text = "Prekliate Dedičstvo"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MenuContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 250.0
grow_horizontal = 2
grow_vertical = 2

[node name="ButtonContainer" type="VBoxContainer" parent="MenuContainer"]
custom_minimum_size = Vector2(400, 0)
layout_mode = 2
theme_override_constants/separation = 15

[node name="NovaHraButton" type="Button" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 60)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_pressed_color = Color(0.8, 0.7, 0.5, 1)
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_2")
theme_override_styles/pressed = SubResource("StyleBoxFlat_2")
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "Nová Hra"

[node name="PokracovatButton" type="Button" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 60)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_pressed_color = Color(0.8, 0.7, 0.5, 1)
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_2")
theme_override_styles/pressed = SubResource("StyleBoxFlat_2")
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "Pokračovať"

[node name="KapitolyButton" type="Button" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 60)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_pressed_color = Color(0.8, 0.7, 0.5, 1)
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_2")
theme_override_styles/pressed = SubResource("StyleBoxFlat_2")
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "Kapitoly"

[node name="NastaveniaButton" type="Button" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 60)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_pressed_color = Color(0.8, 0.7, 0.5, 1)
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_2")
theme_override_styles/pressed = SubResource("StyleBoxFlat_2")
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "Nastavenia"

[node name="OHreButton" type="Button" parent="MenuContainer/ButtonContainer"]
custom_minimum_size = Vector2(400, 60)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_pressed_color = Color(0.8, 0.7, 0.5, 1)
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 24
theme_override_styles/hover = SubResource("StyleBoxFlat_2")
theme_override_styles/pressed = SubResource("StyleBoxFlat_2")
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "O Hre"

[node name="VersionLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -120.0
offset_top = -40.0
offset_right = -20.0
offset_bottom = -10.0
grow_horizontal = 0
grow_vertical = 0
theme_override_colors/font_color = Color(0.6, 0.6, 0.6, 0.8)
theme_override_fonts/font = ExtResource("2_font")
theme_override_font_sizes/font_size = 14
text = "verzia 1.0.0"
horizontal_alignment = 2
vertical_alignment = 2
