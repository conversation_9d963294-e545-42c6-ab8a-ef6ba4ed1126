[gd_scene load_steps=20 format=3 uid="uid://cxm8n9qkrxrxr"]

[ext_resource type="Script" path="res://scripts/NewChaptersMenu.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]
[ext_resource type="Texture2D" uid="uid://cpwk244fogft1" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_main_panel"]
[ext_resource type="Texture2D" uid="uid://bfoshjpk7gun5" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Panel4.png" id="4_list_panel"]
[ext_resource type="Texture2D" uid="uid://c02qs7ydh14rd" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Line Select Current.png" id="5_line_current"]
[ext_resource type="Texture2D" uid="uid://buffgrp4n7myp" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Line Select Hover.png" id="6_line_hover"]
[ext_resource type="Texture2D" uid="uid://dm8x4jmnc17xd" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Mask Img.png" id="7_image_mask"]
[ext_resource type="Texture2D" uid="uid://h4fgxwlt7858" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Normal.png" id="8_button"]
[ext_resource type="Texture2D" uid="uid://6hx86f2x1kth" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Hover.png" id="9_button_hover"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_1.png" id="10_ch1"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_2.png" id="11_ch2"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_3.png" id="12_ch3"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_4.png" id="13_ch4"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_5.png" id="14_ch5"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_6.png" id="15_ch6"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_7.png" id="16_ch7"]
[ext_resource type="Texture2D" path="res://assets/Kapitoly_VISUALS/Kapitola_7.png" id="17_epilog"]
[ext_resource type="Texture2D" uid="uid://0u43hx2s8d5a" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/TextField.png" id="18_textfield"]
[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="19_theme"]
[ext_resource type="Texture2D" uid="uid://byemwgec4lnvk" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Select/Arrow Normal.png" id="20_arrow_normal"]
[ext_resource type="Texture2D" uid="uid://dj4maptea6l35" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Select/Arrow Hover.png" id="21_arrow_hover"]

[node name="ChaptersMenu" type="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("19_theme")
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 60.0
offset_right = -20.0
offset_bottom = -20.0
texture = ExtResource("3_main_panel")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="TitleLabel" type="Label" parent="MainPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -150.0
offset_top = 15.0
offset_right = 150.0
offset_bottom = 50.0
text = "VÝBER KAPITOLY"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MainContainer" type="HBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 60.0
offset_right = -30.0
offset_bottom = -80.0

[node name="LeftPanel" type="VBoxContainer" parent="MainPanel/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 1.2

[node name="ChapterCarouselPanel" type="NinePatchRect" parent="MainPanel/MainContainer/LeftPanel"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("4_list_panel")
patch_margin_left = 15
patch_margin_top = 15
patch_margin_right = 15
patch_margin_bottom = 15

[node name="CarouselContainer" type="VBoxContainer" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_top = 15.0
offset_right = -15.0
offset_bottom = -15.0

[node name="NavigationContainer" type="HBoxContainer" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer"]
layout_mode = 2
alignment = 1

[node name="PrevButton" type="TextureButton" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/NavigationContainer"]
layout_mode = 2
custom_minimum_size = Vector2(40, 40)
texture_normal = ExtResource("20_arrow_normal")
texture_hover = ExtResource("21_arrow_hover")
stretch_mode = 0
flip_h = true

[node name="ChapterIndicator" type="Label" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/NavigationContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "1 / 8"
horizontal_alignment = 1
vertical_alignment = 1

[node name="NextButton" type="TextureButton" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/NavigationContainer"]
layout_mode = 2
custom_minimum_size = Vector2(40, 40)
texture_normal = ExtResource("20_arrow_normal")
texture_hover = ExtResource("21_arrow_hover")
stretch_mode = 0

[node name="Spacer1" type="Control" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="CurrentChapterContainer" type="VBoxContainer" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="CurrentChapterButton" type="TextureButton" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/CurrentChapterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
texture_normal = ExtResource("5_line_current")
texture_hover = ExtResource("6_line_hover")
stretch_mode = 0

[node name="ChapterLabel" type="Label" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/CurrentChapterContainer/CurrentChapterButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_right = -10.0
text = "Kapitola 1 - Cesta na zámok"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer2" type="Control" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ChapterStatusContainer" type="VBoxContainer" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/ChapterStatusContainer"]
layout_mode = 2
text = "ODOMKNUTÉ"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ProgressLabel" type="Label" parent="MainPanel/MainContainer/LeftPanel/ChapterCarouselPanel/CarouselContainer/ChapterStatusContainer"]
layout_mode = 2
text = "Dokončené: 100%"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer" type="Control" parent="MainPanel/MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(20, 0)

[node name="RightPanel" type="VBoxContainer" parent="MainPanel/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 1.0

[node name="ChapterImageContainer" type="Control" parent="MainPanel/MainContainer/RightPanel"]
layout_mode = 2
size_flags_vertical = 3
size_flags_stretch_ratio = 1.2

[node name="ImageMask" type="NinePatchRect" parent="MainPanel/MainContainer/RightPanel/ChapterImageContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("7_image_mask")
patch_margin_left = 15
patch_margin_top = 15
patch_margin_right = 15
patch_margin_bottom = 15

[node name="ChapterImage" type="TextureRect" parent="MainPanel/MainContainer/RightPanel/ChapterImageContainer/ImageMask"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_top = 15.0
offset_right = -15.0
offset_bottom = -15.0
texture = ExtResource("10_ch1")
expand_mode = 1
stretch_mode = 6

[node name="ChapterInfoContainer" type="Control" parent="MainPanel/MainContainer/RightPanel"]
layout_mode = 2
size_flags_vertical = 3
size_flags_stretch_ratio = 1.0

[node name="InfoPanel" type="NinePatchRect" parent="MainPanel/MainContainer/RightPanel/ChapterInfoContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("18_textfield")
patch_margin_left = 15
patch_margin_top = 15
patch_margin_right = 15
patch_margin_bottom = 15

[node name="InfoContent" type="VBoxContainer" parent="MainPanel/MainContainer/RightPanel/ChapterInfoContainer/InfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_top = 15.0
offset_right = -15.0
offset_bottom = -15.0

[node name="ChapterTitle" type="Label" parent="MainPanel/MainContainer/RightPanel/ChapterInfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
text = "Kapitola 1 - Cesta na zámok"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="MainPanel/MainContainer/RightPanel/ChapterInfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
custom_minimum_size = Vector2(0, 10)

[node name="ChapterDescription" type="RichTextLabel" parent="MainPanel/MainContainer/RightPanel/ChapterInfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "Marec 1894. Tvoje kolesá sa krútia po blatistej ceste naprieč karpatskými lesmi, kam ťa zavolal posledný telegram od mentora. V tejto úvodnej kapitole začínaš v kolísajúcom kočiari."
fit_content = true
scroll_active = false

[node name="ButtonsContainer" type="HBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -120.0
offset_top = -60.0
offset_right = 120.0
offset_bottom = -20.0

[node name="PlayButton" type="TextureButton" parent="MainPanel/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 40)
texture_normal = ExtResource("8_button")
texture_hover = ExtResource("9_button_hover")
stretch_mode = 0

[node name="PlayLabel" type="Label" parent="MainPanel/ButtonsContainer/PlayButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "HRAŤ"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer" type="Control" parent="MainPanel/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(20, 0)

[node name="BackButton" type="TextureButton" parent="MainPanel/ButtonsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(100, 40)
texture_normal = ExtResource("8_button")
texture_hover = ExtResource("9_button_hover")
stretch_mode = 0

[node name="BackLabel" type="Label" parent="MainPanel/ButtonsContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1
