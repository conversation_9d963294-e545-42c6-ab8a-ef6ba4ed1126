extends Control

# Save Game Style Chapter Selection Menu

@onready var title_label: Label = $MainPanel/TitleLabel
@onready var chapter_title_label: Label = $MainPanel/MainContainer/RightPanel/ChapterInfoContainer/InfoPanel/InfoContent/ChapterTitle
@onready var chapter_description: RichTextLabel = $MainPanel/MainContainer/RightPanel/ChapterInfoContainer/InfoPanel/InfoContent/ChapterDescription
@onready var chapter_image: TextureRect = $MainPanel/MainContainer/RightPanel/ChapterImageContainer/ImageMask/ChapterImage
@onready var play_button: TextureButton = $MainPanel/ButtonsContainer/PlayButton
@onready var back_button: TextureButton = $MainPanel/ButtonsContainer/BackButton

# Chapter buttons
@onready var chapter_buttons: Array[TextureButton] = []
@onready var chapter_labels: Array[Label] = []

# Button labels
@onready var play_label: Label = $MainPanel/ButtonsContainer/PlayButton/PlayLabel
@onready var back_label: Label = $MainPanel/ButtonsContainer/BackButton/BackLabel

var current_chapter: int = 1
var max_chapters: int = 8  # 7 chapters + epilog

# Obrázky kapitol
var chapter_images = {
	1: preload("res://assets/Kapitoly_VISUALS/Kapitola_1.png"),
	2: preload("res://assets/Kapitoly_VISUALS/Kapitola_2.png"),
	3: preload("res://assets/Kapitoly_VISUALS/Kapitola_3.png"),
	4: preload("res://assets/Kapitoly_VISUALS/Kapitola_4.png"),
	5: preload("res://assets/Kapitoly_VISUALS/Kapitola_5.png"),
	6: preload("res://assets/Kapitoly_VISUALS/Kapitola_6.png"),
	7: preload("res://assets/Kapitoly_VISUALS/Kapitola_7.png"),
	8: preload("res://assets/Kapitoly_VISUALS/Kapitola_7.png")  # Použiť obrázok kapitoly 7 pre epilóg
}

# Textures for selection states
var line_current_texture = preload("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Line Select Current.png")
var line_hover_texture = preload("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Line Select Hover.png")

# Informácie o kapitolách
var chapter_info = {
	1: {
		"title": "Kapitola 1 - Cesta na zámok",
		"description": "Marec 1894. Tvoje kolesá sa krútia po blatistej ceste naprieč karpatskými lesmi, kam ťa zavolal posledný telegram od mentora. V tejto úvodnej kapitole začínaš v kolísajúcom kočiari. Silná búrka triešti krajinu na kúsky, kočiš mlčí, a ty čítaš znepokojujúce poznámky Van Helsinga. Krátka hádanka ti odhalí strašnú vetu: Grófka je v krypte. Keď kočiš odmietne pokračovať, si nútený ísť pešo cez temný les, kde sa cesta delí podľa starého verša. Tma houstne, príroda dýcha niečím neľudským."
	},
	2: {
		"title": "Kapitola 2 - Brána zámku",
		"description": "Pred tebou sa týči zámok – ako mŕtva koruna zabudnutej dynastie. Stojíš pred železnou bránou zdobenou heraldickými symbolmi, pod ktorou tečie voda z topiaceho sa snehu a zo striech ťa pozorujú havrany. Na bráne nájdeš krvavý nápis a lúštiš hádanku, ktorá ti otvorí cestu. Vstupuješ na nádvorie, kde sa z ticha vynorí Viktor – sluha Van Helsinga. Podrobí ťa skúške Rádu Striebornej ruže, kde správne odpovede odomknú tvoju ďalšiu cestu."
	},
	3: {
		"title": "Kapitola 3 - Pátranie v zámku",
		"description": "Za múrmi vládne ticho. Ale nie je to pokoj. Je to čakanie. Zámok pôsobí opustene, ale zdanlivý pokoj ukrýva staré hrozby. Objavuješ vstupnú halu, čítaš denník, v ktorom Van Helsing priznáva – našiel Isabelle. V knižnici objavuješ skrytú priehradku a lúštiš jednoduchý výpočet, ktorý ťa zavedie hlbšie. Zápisy odhaľujú plán: zostup do katakomb, príprava na boj, a odkaz na vonkajšie múry, kde sa má skrývať tajný vchod do starej časti zámku."
	},
	4: {
		"title": "Kapitola 4 - Tajné krídlo",
		"description": "Vzduch je vlhký a ťažký, ako keby si kráčal útrobami mŕtveho obra. Vstupuješ do starého krídla zámku, kde za stenami cvakajú pasce a mechanizmy. Pomáha ti spomienka na básničku o Mesiaci. Čaká ťa farebný pamäťový test a následne vstupuješ do alchymistického laboratória, kde pripravuješ elixír ochrany. Pred sebou máš zostup do krypt. Čas sa kráti. Isabelle vie, že prichádzaš."
	},
	5: {
		"title": "Kapitola 5 - Krypty",
		"description": "Staré schody vedú hlboko pod zem. Vzduch je studený, ako by si zostupoval do srdca smrti. Kamene sú ošúchané, vo vzduchu visí prach a pamäť minulých storočí. Viktor ostáva strážiť. Ty zostupuješ sám. Tiene a čísla ti pomôžu odhaliť kód, ktorý ťa vpustí do pradávnej komnaty. Nájdeš tam lampu, prázdny revolver a zápisník Van Helsinga. Onedlho sa pred tebou objaví sarkofág, zdobený pákami – a v ňom niečo, čo sa ešte stále hýbe."
	},
	6: {
		"title": "Kapitola 6 - Konfrontácia",
		"description": "Pečať sa láme. Isabelle sa prebúdza. A čas rozhodnutia je tu. Keď otvoríš sarkofág, grófka Isabelle Báthoryová sa prebudí po troch storočiach. Už nie je krásna – je čosi iné, pradávne a znetvorené. Hádanka troch sestier preverí tvoju múdrosť. Ak ju rozlúštiš, máš jedinú šancu vykonať rituál podľa poznámok Van Helsinga. Rytmus svätenej vody a symbolov rozhodne, či zvíťazíš alebo zomrieš v jej tieni."
	},
	7: {
		"title": "Kapitola 7 - Záchrana mentora",
		"description": "Nie všetko je stratené. Ale ani celkom vyhraté. Po porážke Isabelle sa odkryje tajná miestnosť, kde nájdeš Van Helsinga – živého, ale nakazeného. Elixír spomalil premenu, no nie vyliečil. Musíte sa dostať do laboratória v Budapešti. Vychádzaš von, prvé ranné svetlo triešti búrkové mračná, a zámok mizne v diaľke. Dedičstvo rodu Báthoryovcov je zlomené. Ale zápas so zlom nikdy nekončí."
	},
	8: {
		"title": "Epilóg - Nový začiatok",
		"description": "Budapešť, jar 1894. Van Helsing sa zotavuje v laboratóriu, kde pracuje na definitívnom lieku. Ty sa vraciaš do normálneho života, ale vieš, že svet je plný tajomstiev a nebezpečenstiev. Dedičstvo Báthoryovcov je síce zlomené, ale existujú aj iné pradávne hrozby. Tvoja cesta lovca nestvôr sa len začína. Priprav sa na nové dobrodružstvá, ktoré ťa čakajú v temných zákutiach Európy."
	}
}

func _ready():
	print("ChaptersMenu načítané")

	# Získanie referencií na chapter buttony
	setup_chapter_buttons()

	# Pripojenie signálov
	connect_signals()

	# Aplikovanie fontov
	apply_fonts()

	# Aktualizovanie zobrazenia
	update_chapter_display()

	# Nastavenie fokusu
	if play_button:
		play_button.grab_focus()

func setup_chapter_buttons():
	"""Nastavenie referencií na chapter buttony"""
	var chapter_list = $MainPanel/MainContainer/LeftPanel/ChapterListPanel/ChapterList

	chapter_buttons.clear()
	chapter_labels.clear()

	for i in range(1, max_chapters + 1):
		var button_name = "Chapter" + str(i) if i <= 7 else "Epilog"
		var button = chapter_list.get_node(button_name)
		var label = button.get_node("Label")

		if button and label:
			chapter_buttons.append(button)
			chapter_labels.append(label)

			# Pripojenie signálu pre každý button
			var chapter_index = i
			button.pressed.connect(_on_chapter_selected.bind(chapter_index))

			# Nastavenie dostupnosti kapitoly
			update_chapter_availability(button, label, chapter_index)

func connect_signals():
	"""Pripojenie signálov"""
	if play_button:
		play_button.pressed.connect(_on_play_pressed)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_fonts():
	"""Aplikuje fonty na UI elementy"""
	if title_label:
		FontLoader.apply_font_style(title_label, "chapter_title")
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
		title_label.add_theme_font_size_override("font_size", 32)

	if chapter_title_label:
		FontLoader.apply_font_style(chapter_title_label, "character_dialogue")
		chapter_title_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		chapter_title_label.add_theme_font_size_override("font_size", 20)

	if chapter_description:
		FontLoader.apply_font_style(chapter_description, "character_dialogue")
		chapter_description.add_theme_color_override("default_color", Color("#E0E0E0"))
		chapter_description.add_theme_font_size_override("normal_font_size", 16)

	# Chapter list labels
	for label in chapter_labels:
		if label:
			FontLoader.apply_font_style(label, "ui_text")
			label.add_theme_color_override("font_color", Color("#F5F5DC"))
			label.add_theme_font_size_override("font_size", 16)

	# Button labels
	if play_label:
		FontLoader.apply_font_style(play_label, "ui_text")
		play_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		play_label.add_theme_font_size_override("font_size", 18)

	if back_label:
		FontLoader.apply_font_style(back_label, "ui_text")
		back_label.add_theme_color_override("font_color", Color("#F5F5DC"))
		back_label.add_theme_font_size_override("font_size", 18)

func update_chapter_display():
	"""Aktualizuje zobrazenie aktuálnej kapitoly"""
	if chapter_title_label and chapter_info.has(current_chapter):
		chapter_title_label.text = chapter_info[current_chapter]["title"]

	if chapter_description and chapter_info.has(current_chapter):
		var description_text = chapter_info[current_chapter]["description"]

		# Pridať štatistiky kapitoly
		var stats_text = get_chapter_stats_text(current_chapter)
		if stats_text != "":
			description_text += "\n\n" + stats_text

		chapter_description.text = description_text

	# Aktualizovanie obrázka kapitoly
	if chapter_image and chapter_images.has(current_chapter):
		chapter_image.texture = chapter_images[current_chapter]

	# Aktualizovanie výberu v zozname kapitol
	update_chapter_selection()

func get_chapter_stats_text(chapter_index: int) -> String:
	"""Vráti text so štatistikami kapitoly"""
	var stats = []

	# Stav kapitoly
	var is_unlocked = GameManager.is_chapter_unlocked(chapter_index) if chapter_index <= 7 else GameManager.is_chapter_unlocked(7)
	var is_completed = GameManager.completed_chapters.has(chapter_index) if chapter_index <= 7 else GameManager.completed_chapters.has(7)

	if not is_unlocked:
		stats.append("[color=#FF6B6B]Stav: ZAMKNUTÉ[/color]")
	elif is_completed:
		stats.append("[color=#90EE90]Stav: DOKONČENÉ ✓[/color]")
	else:
		stats.append("[color=#FFD93D]Stav: DOSTUPNÉ[/color]")

	# Pre kapitoly s hlavolamami (nie epilóg)
	if chapter_index <= 7 and chapter_index != 8:
		var puzzles_completed = 0
		if GameManager.completed_puzzles.has(chapter_index):
			puzzles_completed = GameManager.completed_puzzles[chapter_index].size()

		var total_puzzles = 2 if chapter_index != 7 else 0  # Kapitola 7 nemá hlavolamy
		if total_puzzles > 0:
			stats.append("Hlavolamy: " + str(puzzles_completed) + "/" + str(total_puzzles))

	return "\n".join(stats) if stats.size() > 0 else ""

func update_chapter_availability(button: TextureButton, label: Label, chapter_index: int):
	"""Aktualizuje dostupnosť kapitoly"""
	var is_unlocked = GameManager.is_chapter_unlocked(chapter_index) if chapter_index <= 7 else GameManager.is_chapter_unlocked(7)
	var is_completed = GameManager.completed_chapters.has(chapter_index) if chapter_index <= 7 else GameManager.completed_chapters.has(7)

	if not is_unlocked:
		# Zamknutá kapitola
		button.disabled = true
		label.add_theme_color_override("font_color", Color("#666666"))
		label.text = label.text + " [ZAMKNUTÉ]"
	elif is_completed:
		# Dokončená kapitola
		button.disabled = false
		label.add_theme_color_override("font_color", Color("#90EE90"))  # Svetlo zelená
		label.text = label.text + " ✓"
	else:
		# Dostupná kapitola
		button.disabled = false
		label.add_theme_color_override("font_color", Color("#F5F5DC"))

func update_chapter_selection():
	"""Aktualizuje vizuálny výber v zozname kapitol"""
	for i in range(chapter_buttons.size()):
		var button = chapter_buttons[i]
		var chapter_index = i + 1

		if chapter_index == current_chapter:
			# Aktuálne vybraná kapitola
			button.texture_normal = line_current_texture
		else:
			# Ostatné kapitoly
			button.texture_normal = null

func _on_chapter_selected(chapter_index: int):
	"""Spracovanie výberu kapitoly zo zoznamu"""
	# Skontrolovať, či je kapitola odomknutá
	var is_unlocked = GameManager.is_chapter_unlocked(chapter_index) if chapter_index <= 7 else GameManager.is_chapter_unlocked(7)

	if not is_unlocked:
		print("Kapitola ", chapter_index, " je zamknutá!")
		AudioManager.play_wrong_answer_sound()
		return

	if chapter_index != current_chapter:
		current_chapter = chapter_index
		update_chapter_display()
		AudioManager.play_menu_button_sound()

func _on_play_pressed():
	print("Spúšťam kapitolu ", current_chapter)
	AudioManager.play_menu_button_sound()

	# Pre epilóg (chapter 8) spustiť kapitolu 7, inak normálnu kapitolu
	if current_chapter == 8:
		GameManager.go_to_chapter(7)  # Epilóg je Chapter7.tscn
	else:
		GameManager.go_to_chapter(current_chapter)

func _on_back_pressed():
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_up"):
		_navigate_chapter(-1)
	elif event.is_action_pressed("ui_down"):
		_navigate_chapter(1)
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()

func _navigate_chapter(direction: int):
	"""Navigácia medzi kapitolami pomocou klávesnice"""
	var new_chapter = current_chapter + direction
	if new_chapter >= 1 and new_chapter <= max_chapters:
		current_chapter = new_chapter
		update_chapter_display()
		AudioManager.play_menu_button_sound()
